<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HKEX Leadership - Board Members & Key Management</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/brands.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .controls {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .filter-group {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            align-items: center;
        }

        .filter-item {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .filter-item label {
            font-weight: 600;
            color: #555;
            font-size: 0.9rem;
        }

        select, input {
            padding: 8px 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 0.9rem;
            transition: border-color 0.3s;
        }

        select:focus, input:focus {
            outline: none;
            border-color: #667eea;
        }

        .section-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 30px;
            justify-content: center;
        }

        .tab-button {
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s;
            color: #667eea;
        }

        .tab-button.active {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .leadership-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .person-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .person-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 60px rgba(0,0,0,0.15);
        }

        .person-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .person-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
            border: 3px solid rgba(255,255,255,0.3);
        }

        .person-info h3 {
            color: #333;
            margin-bottom: 5px;
            font-size: 1.1rem;
        }

        .person-title {
            color: #667eea;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .person-details {
            margin-top: 15px;
        }

        .detail-item {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            font-size: 0.85rem;
            color: #666;
        }

        .detail-item i {
            color: #667eea;
            width: 16px;
        }

        .committees {
            margin-top: 15px;
        }

        .committee-tag {
            display: inline-block;
            background: #f0f3ff;
            color: #667eea;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            margin: 2px;
            border: 1px solid #e1e8ff;
        }

        .section {
            display: none;
        }

        .section.active {
            display: block;
        }

        .details-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.85rem;
            font-weight: 500;
            transition: all 0.3s ease;
            margin-top: 10px;
            width: 100%;
        }

        .details-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .person-card {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .person-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 2% auto;
            padding: 0;
            border-radius: 20px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 20px 20px 0 0;
            position: relative;
        }

        .modal-close {
            color: white;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            position: absolute;
            right: 20px;
            top: 20px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.3s ease;
        }

        .modal-close:hover {
            background-color: rgba(255,255,255,0.2);
        }

        .modal-body {
            padding: 30px;
        }

        .modal-person-info {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 20px;
        }

        .modal-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            font-weight: bold;
            color: white;
            flex-shrink: 0;
        }

        .modal-person-details h2 {
            margin: 0 0 5px 0;
            font-size: 1.8rem;
        }

        .modal-person-details .chinese-name {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 5px;
        }

        .modal-person-details .title {
            font-size: 1.1rem;
            opacity: 0.8;
        }

        .modal-section {
            margin-bottom: 25px;
        }

        .modal-section h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.2rem;
            border-bottom: 2px solid #f0f3ff;
            padding-bottom: 8px;
        }

        .modal-list {
            list-style: none;
            padding: 0;
        }

        .modal-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f5f5f5;
            display: flex;
            align-items: flex-start;
            gap: 10px;
        }

        .modal-list li:last-child {
            border-bottom: none;
        }

        .modal-list li i {
            color: #667eea;
            margin-top: 2px;
            width: 16px;
        }

        .portrait-photo {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid rgba(255,255,255,0.3);
        }

        .org-chart {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
        }

        .chart-level {
            margin-bottom: 30px;
        }

        .chart-title {
            text-align: center;
            font-size: 1.2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 20px;
        }

        .chart-row {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .chart-person {
            background: #f8f9ff;
            border: 2px solid #e1e8ff;
            border-radius: 12px;
            padding: 15px;
            text-align: center;
            min-width: 200px;
            transition: all 0.3s;
        }

        .chart-person:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .chart-person h4 {
            color: #333;
            margin-bottom: 5px;
        }

        .chart-person p {
            color: #667eea;
            font-size: 0.85rem;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .leadership-grid {
                grid-template-columns: 1fr;
            }
            
            .filter-group {
                flex-direction: column;
                align-items: stretch;
            }
            
            .section-tabs {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-building"></i> HKEX Leadership</h1>
            <p>Board of Directors & Key Management Team</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="totalBoard">13</div>
                <div class="stat-label">Board Members</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalManagement">13</div>
                <div class="stat-label">Management Committee</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalCommittees">9</div>
                <div class="stat-label">Board Committees</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="independentDirectors">11</div>
                <div class="stat-label">Independent Directors</div>
            </div>
        </div>

        <div class="controls">
            <div class="filter-group">
                <div class="filter-item">
                    <label for="roleFilter">Filter by Role:</label>
                    <select id="roleFilter">
                        <option value="">All Roles</option>
                        <option value="chairman">Chairman</option>
                        <option value="executive">Executive Director</option>
                        <option value="independent">Independent Director</option>
                        <option value="management">Management</option>
                    </select>
                </div>
                <div class="filter-item">
                    <label for="committeeFilter">Filter by Committee:</label>
                    <select id="committeeFilter">
                        <option value="">All Committees</option>
                        <option value="audit">Audit Committee</option>
                        <option value="risk">Risk Committee</option>
                        <option value="remuneration">Remuneration Committee</option>
                        <option value="nomination">Nomination & Governance</option>
                    </select>
                </div>
                <div class="filter-item">
                    <label for="searchFilter">Search:</label>
                    <input type="text" id="searchFilter" placeholder="Search by name or title...">
                </div>
            </div>
        </div>

        <div class="section-tabs">
            <button class="tab-button active" onclick="showSection('board')">
                <i class="fas fa-users"></i> Board of Directors
            </button>
            <button class="tab-button" onclick="showSection('management')">
                <i class="fas fa-user-tie"></i> Management Committee
            </button>
            <button class="tab-button" onclick="showSection('structure')">
                <i class="fas fa-sitemap"></i> Organization Structure
            </button>
        </div>

        <div id="board" class="section active">
            <div class="leadership-grid" id="boardGrid">
                <!-- Board members will be populated by JavaScript -->
            </div>
        </div>

        <div id="management" class="section">
            <div class="leadership-grid" id="managementGrid">
                <!-- Management team will be populated by JavaScript -->
            </div>
        </div>

        <div id="structure" class="section">
            <div class="org-chart">
                <div class="chart-level">
                    <div class="chart-title">Board Level</div>
                    <div class="chart-row">
                        <div class="chart-person">
                            <h4>Carlson TONG<br><span style="font-size: 0.8em; color: #666;">唐家成</span></h4>
                            <p>Chairman</p>
                        </div>
                    </div>
                </div>
                
                <div class="chart-level">
                    <div class="chart-title">Executive Level</div>
                    <div class="chart-row">
                        <div class="chart-person">
                            <h4>Bonnie Y CHAN<br><span style="font-size: 0.8em; color: #666;">陈翊庭</span></h4>
                            <p>CEO & Executive Director</p>
                        </div>
                    </div>
                </div>
                
                <div class="chart-level">
                    <div class="chart-title">Senior Management</div>
                    <div class="chart-row">
                        <div class="chart-person">
                            <h4>Vanessa LAU<br><span style="font-size: 0.8em; color: #666;">刘碧筠</span></h4>
                            <p>Chief Operating Officer</p>
                        </div>
                        <div class="chart-person">
                            <h4>Herbert HUI<br><span style="font-size: 0.8em; color: #666;">许正宇</span></h4>
                            <p>Group CFO</p>
                        </div>
                        <div class="chart-person">
                            <h4>Paul CHOW<br><span style="font-size: 0.8em; color: #666;">周保昌</span></h4>
                            <p>Group General Counsel</p>
                        </div>
                        <div class="chart-person">
                            <h4>John HSU<br><span style="font-size: 0.8em; color: #666;">徐志华</span></h4>
                            <p>Group CTO</p>
                        </div>
                        <div class="chart-person">
                            <h4>Richard WISE<br><span style="font-size: 0.8em; color: #666;">韦志明</span></h4>
                            <p>Group CRO</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for person details -->
    <div id="personModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <span class="modal-close">&times;</span>
                <div class="modal-person-info">
                    <div class="modal-avatar" id="modalAvatar"></div>
                    <div class="modal-person-details">
                        <h2 id="modalName"></h2>
                        <div class="chinese-name" id="modalChineseName"></div>
                        <div class="title" id="modalTitle"></div>
                    </div>
                </div>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- Content will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <script src="hkex-leadership-data-official.js"></script>
    <script src="hkex-leadership-script.js"></script>
</body>
</html>
